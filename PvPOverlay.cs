using System;
using System.Collections.Generic;
using System.Numerics;
using Dalamud.Game.ClientState.Conditions;
using Dalamud.Game.ClientState.Objects.SubKinds;
using ImGuiNET;

namespace PvPLinePlugin;

public sealed class PvPOverlay : IDisposable
{
    private readonly Configuration config;

    public PvPOverlay(Plugin plugin)
    {
        config = plugin.Configuration;
        Plugin.PluginInterface.UiBuilder.Draw += DrawOverlay;
    }

    public void Dispose()
    {
        Plugin.PluginInterface.UiBuilder.Draw -= DrawOverlay;
        GC.SuppressFinalize(this);
    }

    private void DrawOverlay()
    {
        if (!ShouldDrawLines()) return;

        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return;

        var (enemies, allies) = GetPlayersAroundMe(localPlayer);

        if (config.ShowEnemies)
        {
            foreach (var enemy in enemies)
            {
                if (enemy?.IsValid() == true)
                {
                    DrawLineToPlayer(localPlayer, enemy, false);
                }
            }
        }

        if (config.ShowAllies)
        {
            foreach (var ally in allies)
            {
                if (ally?.IsValid() == true)
                {
                    DrawLineToPlayer(localPlayer, ally, true);
                }
            }
        }
    }

    private bool ShouldDrawLines()
    {
        return config.Enabled &&
               Plugin.ClientState.LocalPlayer != null &&
               (!config.OnlyInPvP || IsPvPTerritory(Plugin.ClientState.TerritoryType)) &&
               (!config.ShowInCombatOnly || Plugin.Condition[ConditionFlag.InCombat]);
    }

    private (List<IPlayerCharacter> enemies, List<IPlayerCharacter> allies) GetPlayersAroundMe(IPlayerCharacter localPlayer)
    {
        var enemies = new List<IPlayerCharacter>();
        var allies = new List<IPlayerCharacter>();
        var maxDistanceSquared = config.MaxDistance * config.MaxDistance;

        foreach (var obj in Plugin.ObjectTable)
        {
            if (obj is not IPlayerCharacter player || player.GameObjectId == localPlayer.GameObjectId)
                continue;

            // Use squared distance for better performance
            var distanceSquared = Vector3.DistanceSquared(localPlayer.Position, player.Position);
            if (distanceSquared > maxDistanceSquared) continue;

            if (IsAllyPlayer(player))
                allies.Add(player);
            else if (IsEnemyPlayer(player))
                enemies.Add(player);
        }

        return (enemies, allies);
    }

    private static bool IsAllyPlayer(IPlayerCharacter player)
    {
        if (Plugin.PartyList == null) return false;

        foreach (var partyMember in Plugin.PartyList)
        {
            if (partyMember?.GameObject?.GameObjectId == player.GameObjectId)
                return true;
        }

        // TODO: Add alliance detection for large-scale PvP modes
        // This would require additional game state information
        return false;
    }

    private static bool IsEnemyPlayer(IPlayerCharacter player)
    {
        // If they're an ally, they're not an enemy
        // In PvP zones, assume all non-ally players are enemies
        return !IsAllyPlayer(player);
    }

    private static float GetHealthPercentage(IPlayerCharacter player)
    {
        if (player?.CurrentHp == null || player?.MaxHp == null || player.MaxHp == 0)
            return 100.0f;

        return (float)player.CurrentHp / player.MaxHp * 100.0f;
    }

    private static bool TryGetScreenPositions(IPlayerCharacter localPlayer, IPlayerCharacter otherPlayer,
        out Vector2 startPos, out Vector2 endPos)
    {
        startPos = endPos = Vector2.Zero;

        if (localPlayer?.Position == null || otherPlayer?.Position == null)
            return false;

        if (!Plugin.GameGui.WorldToScreen(localPlayer.Position, out var localScreenPos) ||
            !Plugin.GameGui.WorldToScreen(otherPlayer.Position, out var otherPlayerScreenPos))
            return false;

        if (float.IsNaN(localScreenPos.X) || float.IsNaN(localScreenPos.Y) ||
            float.IsNaN(otherPlayerScreenPos.X) || float.IsNaN(otherPlayerScreenPos.Y))
            return false;

        startPos = new Vector2(localScreenPos.X, localScreenPos.Y);
        endPos = new Vector2(otherPlayerScreenPos.X, otherPlayerScreenPos.Y);
        return true;
    }

    private (Vector4 color, float thickness) GetLineAppearance(PlayerRole playerRole, bool isAlly, bool isLowHealth, bool isVulnerable = false)
    {
        if (isAlly)
        {
            var color = config.DifferentColorsForAllies && config.ColorCodeByRole
                ? BlendColors(JobHelper.GetRoleColor(playerRole), config.AllyLineColor, 0.5f)
                : config.AllyLineColor;
            return (color, config.AllyLineThickness);
        }

        if (isLowHealth)
        {
            var color = config.LowHealthLineColor;
            if (config.PulseKillableTargets)
            {
                var pulseIntensity = (float)(0.7f + 0.3f * Math.Sin(DateTime.Now.Millisecond * 0.01f));
                color = new Vector4(color.X, color.Y, color.Z, color.W * pulseIntensity);
            }
            return (color, config.LowHealthLineThickness);
        }

        if (isVulnerable)
        {
            // Vulnerable enemies get orange lines to indicate opportunity
            var vulnerableColor = new Vector4(1.0f, 0.5f, 0.0f, 1.0f); // Orange
            return (vulnerableColor, config.LineThickness + 1.0f);
        }

        var normalColor = config.ColorCodeByRole ? JobHelper.GetRoleColor(playerRole) : config.LineColor;
        return (normalColor, config.LineThickness);
    }

    private static Vector4 BlendColors(Vector4 color1, Vector4 color2, float ratio)
    {
        return new Vector4(
            color1.X * ratio + color2.X * (1 - ratio),
            color1.Y * ratio + color2.Y * (1 - ratio),
            color1.Z * ratio + color2.Z * (1 - ratio),
            color2.W
        );
    }

    private void DrawLineToPlayer(IPlayerCharacter localPlayer, IPlayerCharacter otherPlayer, bool isAlly)
    {
        if (!TryGetScreenPositions(localPlayer, otherPlayer, out var startPos, out var endPos))
            return;

        var lineLength = Vector2.Distance(startPos, endPos);
        if (lineLength is < 5.0f or > 2000.0f) return;

        var drawList = ImGui.GetBackgroundDrawList();
        var playerRole = JobHelper.GetPlayerRole(otherPlayer);
        var healthPercentage = GetHealthPercentage(otherPlayer);
        var isLowHealth = config.ShowLowHealthIndicator && healthPercentage <= config.LowHealthThreshold && !isAlly;
        var isVulnerable = !isAlly && StatusEffectHelper.IsVulnerable(otherPlayer);
        var isCrowdControlled = StatusEffectHelper.IsCrowdControlled(otherPlayer);

        var (lineColor, lineThickness) = GetLineAppearance(playerRole, isAlly, isLowHealth, isVulnerable);
        var color = ImGui.ColorConvertFloat4ToU32(lineColor);

        drawList.AddLine(startPos, endPos, color, lineThickness);

        if (config.ShowDistance)
        {
            DrawDistanceText(drawList, localPlayer, otherPlayer, startPos, endPos);
        }

        DrawPlayerInfo(drawList, otherPlayer, endPos, playerRole, healthPercentage, isAlly, isLowHealth, isCrowdControlled);
    }

    private static void DrawDistanceText(ImDrawListPtr drawList, IPlayerCharacter localPlayer,
        IPlayerCharacter otherPlayer, Vector2 startPos, Vector2 endPos)
    {
        var distance = Vector3.Distance(localPlayer.Position, otherPlayer.Position);
        var distanceText = $"{distance:F1}y";
        var midPoint = new Vector2((startPos.X + endPos.X) / 2, (startPos.Y + endPos.Y) / 2);

        DrawTextWithBackground(drawList, distanceText, midPoint, Vector4.One, new Vector4(0, 0, 0, 0.7f));
    }

    private void DrawPlayerInfo(ImDrawListPtr drawList, IPlayerCharacter player, Vector2 screenPos,
        PlayerRole playerRole, float healthPercentage, bool isAlly, bool isLowHealth, bool isCrowdControlled)
    {
        var textYOffset = -25f;

        if (config.ShowAllyEnemyIndicator)
        {
            var statusText = isAlly ? "ALLY" : "ENEMY";
            var statusColor = isAlly ? config.AllyLineColor : new Vector4(1.0f, 0.0f, 0.0f, 1.0f);
            var bgColor = isAlly ? new Vector4(0.0f, 0.3f, 0.0f, 0.8f) : new Vector4(0.3f, 0.0f, 0.0f, 0.8f);

            DrawTextWithBackground(drawList, statusText,
                new Vector2(screenPos.X, screenPos.Y + textYOffset), statusColor, bgColor, 3);
            textYOffset -= 20f;
        }

        if (config.ShowLowHealthIndicator && isLowHealth && !isAlly)
        {
            var healthText = config.ShowHealthPercentage ? $"{healthPercentage:F0}% HP" : "KILLABLE";
            var bgAlpha = config.PulseKillableTargets
                ? (float)(0.6f + 0.4f * Math.Sin(DateTime.Now.Millisecond * 0.01f))
                : 0.8f;
            var bgColor = new Vector4(0.8f, 0.0f, 0.0f, bgAlpha);

            DrawTextWithBackground(drawList, healthText,
                new Vector2(screenPos.X, screenPos.Y + textYOffset), config.LowHealthLineColor, bgColor, 3);
            textYOffset -= 20f;
        }

        if (config.ShowPlayerJobs)
        {
            var jobText = config.ShowJobIcons ? JobHelper.GetJobAbbreviation(player) : JobHelper.GetRoleAbbreviation(playerRole);
            var jobColor = config.ColorCodeByRole ? JobHelper.GetRoleColor(playerRole) : Vector4.One;

            DrawTextWithBackground(drawList, jobText,
                new Vector2(screenPos.X, screenPos.Y + textYOffset), jobColor, new Vector4(0, 0, 0, 0.8f), 2);
            textYOffset -= 18f;
        }

        if (config.ShowPlayerNames && player.Name?.TextValue != null)
        {
            DrawTextWithBackground(drawList, player.Name.TextValue,
                new Vector2(screenPos.X, screenPos.Y + textYOffset), Vector4.One, new Vector4(0, 0, 0, 0.7f), 2);
            textYOffset -= 18f;
        }

        // Draw status effects if enabled
        if (config.ShowStatusEffects && StatusEffectHelper.HasImportantStatusEffects(player))
        {
            var statusEffects = StatusEffectHelper.GetImportantStatusEffects(player, config.ShowOnlyImportantStatus);
            DrawStatusEffects(drawList, statusEffects, screenPos, textYOffset, isCrowdControlled);
        }
    }

    private static void DrawTextWithBackground(ImDrawListPtr drawList, string text, Vector2 centerPos,
        Vector4 textColor, Vector4 bgColor, int padding = 2)
    {
        var textSize = ImGui.CalcTextSize(text);
        var textPos = new Vector2(centerPos.X - textSize.X / 2, centerPos.Y - textSize.Y / 2);
        var bgStart = new Vector2(textPos.X - padding, textPos.Y - padding);
        var bgEnd = new Vector2(textPos.X + textSize.X + padding, textPos.Y + textSize.Y + padding);

        drawList.AddRectFilled(bgStart, bgEnd, ImGui.ColorConvertFloat4ToU32(bgColor));
        drawList.AddText(textPos, ImGui.ColorConvertFloat4ToU32(textColor), text);
    }

    private static void DrawStatusEffects(ImDrawListPtr drawList, List<StatusInfo> statusEffects,
        Vector2 screenPos, float startYOffset, bool isCrowdControlled)
    {
        if (statusEffects.Count == 0) return;

        var textYOffset = startYOffset - 20f;
        var maxEffectsToShow = 3; // Limit to prevent clutter
        var effectsShown = 0;

        // Prioritize crowd control and vulnerability effects
        var prioritizedEffects = new List<StatusInfo>();
        var otherEffects = new List<StatusInfo>();

        foreach (var effect in statusEffects)
        {
            if (effect.Name.Contains("Stun") || effect.Name.Contains("Sleep") ||
                effect.Name.Contains("Silence") || effect.Name.Contains("Vulnerability"))
            {
                prioritizedEffects.Add(effect);
            }
            else
            {
                otherEffects.Add(effect);
            }
        }

        // Draw prioritized effects first
        foreach (var effect in prioritizedEffects)
        {
            if (effectsShown >= maxEffectsToShow) break;

            var displayText = effect.RemainingTime.HasValue
                ? $"{effect.Name} ({effect.RemainingTime.Value:F0}s)"
                : effect.Name;

            var bgColor = effect.IsNegative
                ? new Vector4(0.3f, 0.0f, 0.0f, 0.8f)
                : new Vector4(0.0f, 0.3f, 0.0f, 0.8f);

            // Add pulsing effect for crowd control
            if (isCrowdControlled && effect.IsNegative)
            {
                var pulseIntensity = (float)(0.7f + 0.3f * Math.Sin(DateTime.Now.Millisecond * 0.02f));
                bgColor = new Vector4(bgColor.X, bgColor.Y, bgColor.Z, bgColor.W * pulseIntensity);
            }

            DrawTextWithBackground(drawList, displayText,
                new Vector2(screenPos.X, screenPos.Y + textYOffset), effect.Color, bgColor, 2);

            textYOffset -= 16f;
            effectsShown++;
        }

        // Draw other effects if space remains
        foreach (var effect in otherEffects)
        {
            if (effectsShown >= maxEffectsToShow) break;

            var displayText = effect.RemainingTime.HasValue
                ? $"{effect.Name} ({effect.RemainingTime.Value:F0}s)"
                : effect.Name;

            var bgColor = effect.IsNegative
                ? new Vector4(0.3f, 0.0f, 0.0f, 0.8f)
                : new Vector4(0.0f, 0.3f, 0.0f, 0.8f);

            DrawTextWithBackground(drawList, displayText,
                new Vector2(screenPos.X, screenPos.Y + textYOffset), effect.Color, bgColor, 2);

            textYOffset -= 16f;
            effectsShown++;
        }
    }

    private static bool IsPvPTerritory(uint territoryType)
    {
        // Common PvP territory IDs
        return territoryType switch
        {
            // Frontlines
            376 => true, // Seal Rock
            554 => true, // The Fields of Glory (Shatter)
            692 => true, // The Borderland Ruins (Secure)
            
            // Rival Wings
            691 => true, // Astragalos
            789 => true, // Hidden Gorge
            
            // Crystalline Conflict
            1002 => true, // Crystalline Conflict
            
            // The Wolves' Den
            250 => true, // The Wolves' Den Pier
            
            _ => false
        };
    }
}
