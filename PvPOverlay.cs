using System;
using System.Collections.Generic;
using System.Numerics;
using Dalamud.Game.ClientState.Conditions;
using Dalamud.Game.ClientState.Objects.SubKinds;
using ImGuiNET;

namespace PvPLinePlugin;

public class PvPOverlay : IDisposable
{
    private readonly Configuration config;
    private DateTime lastUpdate = DateTime.MinValue;
    private List<IPlayerCharacter> cachedEnemies = [];

    public PvPOverlay(Plugin plugin)
    {
        config = plugin.Configuration;

        // Subscribe to the UI draw event
        Plugin.PluginInterface.UiBuilder.Draw += DrawOverlay;
    }

    public void Dispose()
    {
        Plugin.PluginInterface.UiBuilder.Draw -= DrawOverlay;
        GC.SuppressFinalize(this);
    }

    private void DrawOverlay()
    {
        // Check if we should draw anything
        if (!ShouldDrawLines()) return;

        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return;

        // Update enemy list based on throttling
        var now = DateTime.Now;
        if ((now - lastUpdate).TotalMilliseconds >= config.UpdateFrequency)
        {
            lastUpdate = now;
            cachedEnemies = GetEnemyPlayers(localPlayer);
        }

        // Always draw from cached list to prevent flickering
        foreach (var enemy in cachedEnemies)
        {
            // Verify enemy is still valid before drawing
            if (enemy != null && enemy.IsValid())
            {
                DrawLineToEnemy(localPlayer, enemy);
            }
        }
    }

    private bool ShouldDrawLines()
    {
        if (!config.Enabled) return false;
        
        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return false;

        // Check if only in PvP zones
        if (config.OnlyInPvP && !IsPvPTerritory(Plugin.ClientState.TerritoryType))
            return false;

        // Check if only during combat
        if (config.ShowInCombatOnly && !Plugin.Condition[ConditionFlag.InCombat])
            return false;

        return true;
    }

    private List<IPlayerCharacter> GetEnemyPlayers(IPlayerCharacter localPlayer)
    {
        var enemies = new List<IPlayerCharacter>();

        foreach (var obj in Plugin.ObjectTable)
        {
            if (obj is not IPlayerCharacter player) continue;
            if (player.GameObjectId == localPlayer.GameObjectId) continue; // Skip self

            // Check if it's an enemy player
            if (!IsEnemyPlayer()) continue;

            // Check distance
            var distance = Vector3.Distance(localPlayer.Position, player.Position);
            if (distance > config.MaxDistance) continue;

            enemies.Add(player);
        }

        return enemies;
    }

    private static bool IsEnemyPlayer()
    {
        // In PvP, players from different Grand Companies or teams are enemies
        // This is a simplified check - you might need more sophisticated logic
        // depending on the specific PvP mode

        // For now, assume all other players are enemies in PvP zones
        // You can enhance this logic based on party/alliance membership
        // or other game state information as needed

        return true;
    }

    private static float GetHealthPercentage(IPlayerCharacter player)
    {
        try
        {
            if (player?.CurrentHp == null || player?.MaxHp == null || player.MaxHp == 0)
                return 100.0f; // Default to full health if we can't read it

            return (float)player.CurrentHp / player.MaxHp * 100.0f;
        }
        catch
        {
            // If we can't read health for any reason, assume full health
            return 100.0f;
        }
    }

    private void DrawLineToEnemy(IPlayerCharacter localPlayer, IPlayerCharacter enemy)
    {
        try
        {
            // Validate positions before attempting to draw
            if (localPlayer?.Position == null || enemy?.Position == null) return;

            // Convert world positions to screen coordinates
            if (!Plugin.GameGui.WorldToScreen(localPlayer.Position, out var localScreenPos)) return;
            if (!Plugin.GameGui.WorldToScreen(enemy.Position, out var enemyScreenPos)) return;

            // Validate screen coordinates are reasonable
            if (float.IsNaN(localScreenPos.X) || float.IsNaN(localScreenPos.Y) ||
                float.IsNaN(enemyScreenPos.X) || float.IsNaN(enemyScreenPos.Y)) return;

            var drawList = ImGui.GetBackgroundDrawList();

            // Convert to Vector2 for ImGui
            var startPos = new Vector2(localScreenPos.X, localScreenPos.Y);
            var endPos = new Vector2(enemyScreenPos.X, enemyScreenPos.Y);

            // Validate the line isn't too short or too long (prevents visual glitches)
            var lineLength = Vector2.Distance(startPos, endPos);
            if (lineLength < 5.0f || lineLength > 2000.0f) return;

            // Get enemy role and health information
            var enemyRole = JobHelper.GetPlayerRole(enemy);
            var healthPercentage = GetHealthPercentage(enemy);
            var isLowHealth = config.ShowLowHealthIndicator && healthPercentage <= config.LowHealthThreshold;

            // Determine line color and thickness based on health and role
            Vector4 lineColor;
            float lineThickness;

            if (isLowHealth)
            {
                // Use low health color and thickness for killable targets
                lineColor = config.LowHealthLineColor;
                lineThickness = config.LowHealthLineThickness;

                // Add pulsing effect if enabled
                if (config.PulseKillableTargets)
                {
                    var pulseIntensity = (float)(0.7f + 0.3f * Math.Sin(DateTime.Now.Millisecond * 0.01f));
                    lineColor = new Vector4(lineColor.X, lineColor.Y, lineColor.Z, lineColor.W * pulseIntensity);
                }
            }
            else
            {
                // Use normal color and thickness
                lineColor = config.ColorCodeByRole ? JobHelper.GetRoleColor(enemyRole) : config.LineColor;
                lineThickness = config.LineThickness;
            }

            var color = ImGui.ColorConvertFloat4ToU32(lineColor);

            // Draw the line with anti-aliasing
            drawList.AddLine(startPos, endPos, color, lineThickness);

            // Draw distance text if enabled
            if (config.ShowDistance)
            {
                var distance = Vector3.Distance(localPlayer.Position, enemy.Position);
                var distanceText = $"{distance:F1}y";

                // Position text at the midpoint of the line
                var midPoint = new Vector2(
                    (startPos.X + endPos.X) / 2,
                    (startPos.Y + endPos.Y) / 2
                );

                // Add background for better text readability
                var textSize = ImGui.CalcTextSize(distanceText);
                var textBg = new Vector2(midPoint.X - textSize.X / 2 - 2, midPoint.Y - textSize.Y / 2 - 1);
                var textBgEnd = new Vector2(midPoint.X + textSize.X / 2 + 2, midPoint.Y + textSize.Y / 2 + 1);
                drawList.AddRectFilled(textBg, textBgEnd, ImGui.ColorConvertFloat4ToU32(new Vector4(0, 0, 0, 0.7f)));

                drawList.AddText(new Vector2(midPoint.X - textSize.X / 2, midPoint.Y - textSize.Y / 2),
                    ImGui.ColorConvertFloat4ToU32(new Vector4(1, 1, 1, 1)), distanceText);
            }

            // Draw player information if enabled
            var textYOffset = -25f;

            // Draw health information if enabled and low health
            if (config.ShowLowHealthIndicator && isLowHealth)
            {
                var healthText = config.ShowHealthPercentage ? $"{healthPercentage:F0}% HP" : "KILLABLE";
                var healthColor = config.LowHealthLineColor;
                var healthSize = ImGui.CalcTextSize(healthText);
                var healthPos = new Vector2(enemyScreenPos.X - healthSize.X / 2, enemyScreenPos.Y + textYOffset);

                // Add pulsing background for killable targets
                var bgAlpha = config.PulseKillableTargets ?
                    (float)(0.6f + 0.4f * Math.Sin(DateTime.Now.Millisecond * 0.01f)) : 0.8f;
                var healthBg = new Vector2(healthPos.X - 3, healthPos.Y - 2);
                var healthBgEnd = new Vector2(healthPos.X + healthSize.X + 3, healthPos.Y + healthSize.Y + 2);
                drawList.AddRectFilled(healthBg, healthBgEnd, ImGui.ColorConvertFloat4ToU32(new Vector4(0.8f, 0.0f, 0.0f, bgAlpha)));

                drawList.AddText(healthPos, ImGui.ColorConvertFloat4ToU32(healthColor), healthText);
                textYOffset -= 20f; // Move next text up
            }

            // Draw job information if enabled
            if (config.ShowPlayerJobs)
            {
                var jobText = config.ShowJobIcons ? JobHelper.GetJobAbbreviation(enemy) : JobHelper.GetRoleAbbreviation(enemyRole);
                var jobColor = config.ColorCodeByRole ? JobHelper.GetRoleColor(enemyRole) : new Vector4(1, 1, 1, 1);
                var jobSize = ImGui.CalcTextSize(jobText);
                var jobPos = new Vector2(enemyScreenPos.X - jobSize.X / 2, enemyScreenPos.Y + textYOffset);

                // Add background for better text readability
                var jobBg = new Vector2(jobPos.X - 2, jobPos.Y - 1);
                var jobBgEnd = new Vector2(jobPos.X + jobSize.X + 2, jobPos.Y + jobSize.Y + 1);
                drawList.AddRectFilled(jobBg, jobBgEnd, ImGui.ColorConvertFloat4ToU32(new Vector4(0, 0, 0, 0.8f)));

                drawList.AddText(jobPos, ImGui.ColorConvertFloat4ToU32(jobColor), jobText);
                textYOffset -= 18f; // Move next text up
            }

            // Draw player name if enabled
            if (config.ShowPlayerNames && enemy.Name?.TextValue != null)
            {
                var nameText = enemy.Name.TextValue;
                var nameSize = ImGui.CalcTextSize(nameText);
                var namePos = new Vector2(enemyScreenPos.X - nameSize.X / 2, enemyScreenPos.Y + textYOffset);

                // Add background for better text readability
                var nameBg = new Vector2(namePos.X - 2, namePos.Y - 1);
                var nameBgEnd = new Vector2(namePos.X + nameSize.X + 2, namePos.Y + nameSize.Y + 1);
                drawList.AddRectFilled(nameBg, nameBgEnd, ImGui.ColorConvertFloat4ToU32(new Vector4(0, 0, 0, 0.7f)));

                drawList.AddText(namePos, ImGui.ColorConvertFloat4ToU32(new Vector4(1, 1, 1, 1)), nameText);
            }
        }
        catch
        {
            // Silently handle any drawing errors to prevent crashes
            // This prevents issues when objects become invalid mid-frame
        }
    }

    private static bool IsPvPTerritory(uint territoryType)
    {
        // Common PvP territory IDs
        return territoryType switch
        {
            // Frontlines
            376 => true, // Seal Rock
            554 => true, // The Fields of Glory (Shatter)
            692 => true, // The Borderland Ruins (Secure)
            
            // Rival Wings
            691 => true, // Astragalos
            789 => true, // Hidden Gorge
            
            // Crystalline Conflict
            1002 => true, // Crystalline Conflict
            
            // The Wolves' Den
            250 => true, // The Wolves' Den Pier
            
            _ => false
        };
    }
}
