using System;
using System.Collections.Generic;
using System.Numerics;
using Dalamud.Game.ClientState.Conditions;
using Dalamud.Game.ClientState.Objects.SubKinds;
using ImGuiNET;

namespace PvPLinePlugin;

public class PvPOverlay : IDisposable
{
    private readonly Configuration config;

    public PvPOverlay(Plugin plugin)
    {
        config = plugin.Configuration;

        // Subscribe to the UI draw event
        Plugin.PluginInterface.UiBuilder.Draw += DrawOverlay;
    }

    public void Dispose()
    {
        Plugin.PluginInterface.UiBuilder.Draw -= DrawOverlay;
        GC.SuppressFinalize(this);
    }

    private void DrawOverlay()
    {
        // Check if we should draw anything
        if (!ShouldDrawLines()) return;

        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return;

        // Get current players around me
        var (enemies, allies) = GetPlayersAroundMe(localPlayer);

        // Draw lines to enemies if enabled
        if (config.ShowEnemies)
        {
            foreach (var enemy in enemies)
            {
                // Verify enemy is still valid before drawing
                if (enemy != null && enemy.IsValid())
                {
                    DrawLineToPlayer(localPlayer, enemy, isAlly: false);
                }
            }
        }

        // Draw lines to allies if enabled
        if (config.ShowAllies)
        {
            foreach (var ally in allies)
            {
                // Verify ally is still valid before drawing
                if (ally != null && ally.IsValid())
                {
                    DrawLineToPlayer(localPlayer, ally, isAlly: true);
                }
            }
        }
    }

    private bool ShouldDrawLines()
    {
        if (!config.Enabled) return false;
        
        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return false;

        // Check if only in PvP zones
        if (config.OnlyInPvP && !IsPvPTerritory(Plugin.ClientState.TerritoryType))
            return false;

        // Check if only during combat
        if (config.ShowInCombatOnly && !Plugin.Condition[ConditionFlag.InCombat])
            return false;

        return true;
    }

    private (List<IPlayerCharacter> enemies, List<IPlayerCharacter> allies) GetPlayersAroundMe(IPlayerCharacter localPlayer)
    {
        var enemies = new List<IPlayerCharacter>();
        var allies = new List<IPlayerCharacter>();

        foreach (var obj in Plugin.ObjectTable)
        {
            if (obj is not IPlayerCharacter player) continue;
            if (player.GameObjectId == localPlayer.GameObjectId) continue; // Skip self

            // Check distance first to avoid unnecessary processing
            var distance = Vector3.Distance(localPlayer.Position, player.Position);
            if (distance > config.MaxDistance) continue;

            // Determine if player is ally or enemy
            if (IsAllyPlayer(localPlayer, player))
            {
                allies.Add(player);
            }
            else if (IsEnemyPlayer(localPlayer, player))
            {
                enemies.Add(player);
            }
        }

        return (enemies, allies);
    }

    private static bool IsAllyPlayer(IPlayerCharacter localPlayer, IPlayerCharacter otherPlayer)
    {
        try
        {
            // Check if player is in our party
            if (Plugin.PartyList != null)
            {
                foreach (var partyMember in Plugin.PartyList)
                {
                    if (partyMember?.GameObject?.GameObjectId == otherPlayer.GameObjectId)
                        return true;
                }
            }

            // Check if player is in our alliance (for 24-man content)
            // Note: This is a simplified check. In actual PvP, alliance detection
            // might require more sophisticated logic depending on the game mode

            // For Frontlines and other large-scale PvP, players from the same Grand Company
            // are typically allies. However, this information might not be easily accessible
            // through the current API, so we'll use a more basic approach for now.

            // Additional check: In some PvP modes, you might want to check for alliance members
            // This would require additional Dalamud services or game state information

            // If we can't determine alliance status definitively, assume they're not an ally
            // This is safer than incorrectly marking enemies as allies
            return false;
        }
        catch
        {
            // If any error occurs, assume not an ally for safety
            return false;
        }
    }

    private static bool IsEnemyPlayer(IPlayerCharacter localPlayer, IPlayerCharacter otherPlayer)
    {
        try
        {
            // If they're an ally, they're not an enemy
            if (IsAllyPlayer(localPlayer, otherPlayer))
                return false;

            // In PvP zones, assume all non-ally players are enemies
            // This is a reasonable assumption for most PvP content
            return true;
        }
        catch
        {
            // If any error occurs, assume they're an enemy for safety in PvP
            return true;
        }
    }

    private static float GetHealthPercentage(IPlayerCharacter player)
    {
        try
        {
            if (player?.CurrentHp == null || player?.MaxHp == null || player.MaxHp == 0)
                return 100.0f; // Default to full health if we can't read it

            return (float)player.CurrentHp / player.MaxHp * 100.0f;
        }
        catch
        {
            // If we can't read health for any reason, assume full health
            return 100.0f;
        }
    }

    private void DrawLineToPlayer(IPlayerCharacter localPlayer, IPlayerCharacter otherPlayer, bool isAlly)
    {
        try
        {
            // Validate positions before attempting to draw
            if (localPlayer?.Position == null || otherPlayer?.Position == null) return;

            // Convert world positions to screen coordinates
            if (!Plugin.GameGui.WorldToScreen(localPlayer.Position, out var localScreenPos)) return;
            if (!Plugin.GameGui.WorldToScreen(otherPlayer.Position, out var otherPlayerScreenPos)) return;

            // Validate screen coordinates are reasonable
            if (float.IsNaN(localScreenPos.X) || float.IsNaN(localScreenPos.Y) ||
                float.IsNaN(otherPlayerScreenPos.X) || float.IsNaN(otherPlayerScreenPos.Y)) return;

            var drawList = ImGui.GetBackgroundDrawList();

            // Convert to Vector2 for ImGui
            var startPos = new Vector2(localScreenPos.X, localScreenPos.Y);
            var endPos = new Vector2(otherPlayerScreenPos.X, otherPlayerScreenPos.Y);

            // Validate the line isn't too short or too long (prevents visual glitches)
            var lineLength = Vector2.Distance(startPos, endPos);
            if (lineLength < 5.0f || lineLength > 2000.0f) return;

            // Get player role and health information
            var playerRole = JobHelper.GetPlayerRole(otherPlayer);
            var healthPercentage = GetHealthPercentage(otherPlayer);
            var isLowHealth = config.ShowLowHealthIndicator && healthPercentage <= config.LowHealthThreshold && !isAlly; // Only show low health for enemies

            // Determine line color and thickness based on ally/enemy status, health, and role
            Vector4 lineColor;
            float lineThickness;

            if (isAlly)
            {
                // Use ally colors and settings
                if (config.DifferentColorsForAllies && config.ColorCodeByRole)
                {
                    // Use role color but with ally tint (more green/blue)
                    var roleColor = JobHelper.GetRoleColor(playerRole);
                    lineColor = new Vector4(
                        roleColor.X * 0.5f + config.AllyLineColor.X * 0.5f,
                        roleColor.Y * 0.5f + config.AllyLineColor.Y * 0.5f,
                        roleColor.Z * 0.5f + config.AllyLineColor.Z * 0.5f,
                        config.AllyLineColor.W
                    );
                }
                else
                {
                    lineColor = config.AllyLineColor;
                }
                lineThickness = config.AllyLineThickness;
            }
            else if (isLowHealth)
            {
                // Use low health color and thickness for killable enemies
                lineColor = config.LowHealthLineColor;
                lineThickness = config.LowHealthLineThickness;

                // Add pulsing effect if enabled
                if (config.PulseKillableTargets)
                {
                    var pulseIntensity = (float)(0.7f + 0.3f * Math.Sin(DateTime.Now.Millisecond * 0.01f));
                    lineColor = new Vector4(lineColor.X, lineColor.Y, lineColor.Z, lineColor.W * pulseIntensity);
                }
            }
            else
            {
                // Use normal enemy color and thickness
                lineColor = config.ColorCodeByRole ? JobHelper.GetRoleColor(playerRole) : config.LineColor;
                lineThickness = config.LineThickness;
            }

            var color = ImGui.ColorConvertFloat4ToU32(lineColor);

            // Draw the line with anti-aliasing
            drawList.AddLine(startPos, endPos, color, lineThickness);

            // Draw distance text if enabled
            if (config.ShowDistance)
            {
                var distance = Vector3.Distance(localPlayer.Position, otherPlayer.Position);
                var distanceText = $"{distance:F1}y";

                // Position text at the midpoint of the line
                var midPoint = new Vector2(
                    (startPos.X + endPos.X) / 2,
                    (startPos.Y + endPos.Y) / 2
                );

                // Add background for better text readability
                var textSize = ImGui.CalcTextSize(distanceText);
                var textBg = new Vector2(midPoint.X - textSize.X / 2 - 2, midPoint.Y - textSize.Y / 2 - 1);
                var textBgEnd = new Vector2(midPoint.X + textSize.X / 2 + 2, midPoint.Y + textSize.Y / 2 + 1);
                drawList.AddRectFilled(textBg, textBgEnd, ImGui.ColorConvertFloat4ToU32(new Vector4(0, 0, 0, 0.7f)));

                drawList.AddText(new Vector2(midPoint.X - textSize.X / 2, midPoint.Y - textSize.Y / 2),
                    ImGui.ColorConvertFloat4ToU32(new Vector4(1, 1, 1, 1)), distanceText);
            }

            // Draw player information if enabled
            var textYOffset = -25f;

            // Draw ally/enemy indicator if enabled
            if (config.ShowAllyEnemyIndicator)
            {
                var statusText = isAlly ? "ALLY" : "ENEMY";
                var statusColor = isAlly ? config.AllyLineColor : new Vector4(1.0f, 0.0f, 0.0f, 1.0f);
                var statusSize = ImGui.CalcTextSize(statusText);
                var statusPos = new Vector2(otherPlayerScreenPos.X - statusSize.X / 2, otherPlayerScreenPos.Y + textYOffset);

                // Add background for better text readability
                var statusBg = new Vector2(statusPos.X - 3, statusPos.Y - 2);
                var statusBgEnd = new Vector2(statusPos.X + statusSize.X + 3, statusPos.Y + statusSize.Y + 2);
                var bgColor = isAlly ? new Vector4(0.0f, 0.3f, 0.0f, 0.8f) : new Vector4(0.3f, 0.0f, 0.0f, 0.8f);
                drawList.AddRectFilled(statusBg, statusBgEnd, ImGui.ColorConvertFloat4ToU32(bgColor));

                drawList.AddText(statusPos, ImGui.ColorConvertFloat4ToU32(statusColor), statusText);
                textYOffset -= 20f; // Move next text up
            }

            // Draw health information if enabled and low health (enemies only)
            if (config.ShowLowHealthIndicator && isLowHealth && !isAlly)
            {
                var healthText = config.ShowHealthPercentage ? $"{healthPercentage:F0}% HP" : "KILLABLE";
                var healthColor = config.LowHealthLineColor;
                var healthSize = ImGui.CalcTextSize(healthText);
                var healthPos = new Vector2(otherPlayerScreenPos.X - healthSize.X / 2, otherPlayerScreenPos.Y + textYOffset);

                // Add pulsing background for killable targets
                var bgAlpha = config.PulseKillableTargets ?
                    (float)(0.6f + 0.4f * Math.Sin(DateTime.Now.Millisecond * 0.01f)) : 0.8f;
                var healthBg = new Vector2(healthPos.X - 3, healthPos.Y - 2);
                var healthBgEnd = new Vector2(healthPos.X + healthSize.X + 3, healthPos.Y + healthSize.Y + 2);
                drawList.AddRectFilled(healthBg, healthBgEnd, ImGui.ColorConvertFloat4ToU32(new Vector4(0.8f, 0.0f, 0.0f, bgAlpha)));

                drawList.AddText(healthPos, ImGui.ColorConvertFloat4ToU32(healthColor), healthText);
                textYOffset -= 20f; // Move next text up
            }

            // Draw job information if enabled
            if (config.ShowPlayerJobs)
            {
                var jobText = config.ShowJobIcons ? JobHelper.GetJobAbbreviation(otherPlayer) : JobHelper.GetRoleAbbreviation(playerRole);
                var jobColor = config.ColorCodeByRole ? JobHelper.GetRoleColor(playerRole) : new Vector4(1, 1, 1, 1);
                var jobSize = ImGui.CalcTextSize(jobText);
                var jobPos = new Vector2(otherPlayerScreenPos.X - jobSize.X / 2, otherPlayerScreenPos.Y + textYOffset);

                // Add background for better text readability
                var jobBg = new Vector2(jobPos.X - 2, jobPos.Y - 1);
                var jobBgEnd = new Vector2(jobPos.X + jobSize.X + 2, jobPos.Y + jobSize.Y + 1);
                drawList.AddRectFilled(jobBg, jobBgEnd, ImGui.ColorConvertFloat4ToU32(new Vector4(0, 0, 0, 0.8f)));

                drawList.AddText(jobPos, ImGui.ColorConvertFloat4ToU32(jobColor), jobText);
                textYOffset -= 18f; // Move next text up
            }

            // Draw player name if enabled
            if (config.ShowPlayerNames && otherPlayer.Name?.TextValue != null)
            {
                var nameText = otherPlayer.Name.TextValue;
                var nameSize = ImGui.CalcTextSize(nameText);
                var namePos = new Vector2(otherPlayerScreenPos.X - nameSize.X / 2, otherPlayerScreenPos.Y + textYOffset);

                // Add background for better text readability
                var nameBg = new Vector2(namePos.X - 2, namePos.Y - 1);
                var nameBgEnd = new Vector2(namePos.X + nameSize.X + 2, namePos.Y + nameSize.Y + 1);
                drawList.AddRectFilled(nameBg, nameBgEnd, ImGui.ColorConvertFloat4ToU32(new Vector4(0, 0, 0, 0.7f)));

                drawList.AddText(namePos, ImGui.ColorConvertFloat4ToU32(new Vector4(1, 1, 1, 1)), nameText);
            }
        }
        catch
        {
            // Silently handle any drawing errors to prevent crashes
            // This prevents issues when objects become invalid mid-frame
        }
    }

    private static bool IsPvPTerritory(uint territoryType)
    {
        // Common PvP territory IDs
        return territoryType switch
        {
            // Frontlines
            376 => true, // Seal Rock
            554 => true, // The Fields of Glory (Shatter)
            692 => true, // The Borderland Ruins (Secure)
            
            // Rival Wings
            691 => true, // Astragalos
            789 => true, // Hidden Gorge
            
            // Crystalline Conflict
            1002 => true, // Crystalline Conflict
            
            // The Wolves' Den
            250 => true, // The Wolves' Den Pier
            
            _ => false
        };
    }
}
