using System;
using System.Numerics;
using Dalamud.Interface.Windowing;
using ImGuiNET;

namespace PvPLinePlugin.Windows;

public class ConfigWindow : Window, IDisposable
{
    private Configuration Configuration;
    private Plugin Plugin;

    public ConfigWindow(Plugin plugin) : base("PvP Line Plugin Configuration###PvPLinePluginConfig")
    {
        Flags = ImGuiWindowFlags.NoCollapse;

        Size = new Vector2(650, 700);
        SizeCondition = ImGuiCond.FirstUseEver;

        Configuration = plugin.Configuration;
        Plugin = plugin;
    }

    public void Dispose() { }

    public override void Draw()
    {
        // Main enable/disable at the top
        var enabled = Configuration.Enabled;
        if (ImGui.Checkbox("Enable PvP Lines", ref enabled))
        {
            Configuration.Enabled = enabled;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Master on/off switch for the entire plugin.\nDisable when you don't want any lines displayed.");
        }

        ImGui.Separator();
        ImGui.Spacing();

        // Tab bar for organized settings
        if (ImGui.BeginTabBar("PvPLinePluginTabs"))
        {
            DrawAppearanceTab();
            DrawDistanceTab();
            DrawPvPSettingsTab();
            DrawEnemyAllyTab();
            DrawJobRoleTab();
            DrawLowHealthTab();
            DrawPerformanceTab();
            DrawPresetsTab();

            ImGui.EndTabBar();
        }
    }

    private void DrawAppearanceTab()
    {
        if (ImGui.BeginTabItem("Appearance"))
        {
            ImGui.TextWrapped("Customize how the lines look on your screen.");
            ImGui.Spacing();

            var lineColor = Configuration.LineColor;
            if (ImGui.ColorEdit4("Line Color", ref lineColor))
            {
                Configuration.LineColor = lineColor;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Choose the color and transparency of lines.\nRecommended: Red for aggression, Yellow for visibility, Blue for subtlety.\nLower alpha (transparency) makes lines less intrusive.");
            }

            var lineThickness = Configuration.LineThickness;
            if (ImGui.SliderFloat("Line Thickness", ref lineThickness, 1.0f, 10.0f))
            {
                Configuration.LineThickness = lineThickness;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Controls how thick the lines appear.\n1-2px: Subtle, minimal\n3-4px: Good balance\n5-7px: High visibility\n8-10px: Maximum visibility but may be distracting");
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawDistanceTab()
    {
        if (ImGui.BeginTabItem("Distance"))
        {
            ImGui.TextWrapped("Control how far lines reach and distance information display.");
            ImGui.Spacing();

            var maxDistance = Configuration.MaxDistance;
            if (ImGui.SliderFloat("Max Distance (yalms)", ref maxDistance, 10.0f, 100.0f))
            {
                Configuration.MaxDistance = maxDistance;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Only draws lines to enemies within this distance.\n20-30y: Close combat (melee DPS, tanks)\n40-50y: Balanced for most jobs\n60-80y: Long-range (casters, ranged DPS)\n100y: Maximum awareness (may cause clutter)");
            }

            var showDistance = Configuration.ShowDistance;
            if (ImGui.Checkbox("Show Distance Text", ref showDistance))
            {
                Configuration.ShowDistance = showDistance;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays actual distance in yalms next to each line.\nUseful for learning optimal engagement ranges.\nDisable to reduce screen clutter.");
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawPvPSettingsTab()
    {
        if (ImGui.BeginTabItem("PvP Settings"))
        {
            ImGui.TextWrapped("Configure when and how the plugin activates in PvP content.");
            ImGui.Spacing();

            var onlyInPvP = Configuration.OnlyInPvP;
            if (ImGui.Checkbox("Only Show in PvP Zones", ref onlyInPvP))
            {
                Configuration.OnlyInPvP = onlyInPvP;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Restricts plugin to PvP areas only (Frontlines, Rival Wings, Crystalline Conflict, Wolves' Den).\nRecommended: Keep enabled to avoid marking friendly players as enemies in PvE content.");
            }

            var showInCombatOnly = Configuration.ShowInCombatOnly;
            if (ImGui.Checkbox("Only Show During Combat", ref showInCombatOnly))
            {
                Configuration.ShowInCombatOnly = showInCombatOnly;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Lines only appear when you're actively in combat (hotbars turn red).\nEnable: Reduces visual noise during downtime\nDisable: Constant enemy awareness and positioning");
            }

            var showPlayerNames = Configuration.ShowPlayerNames;
            if (ImGui.Checkbox("Show Enemy Player Names", ref showPlayerNames))
            {
                Configuration.ShowPlayerNames = showPlayerNames;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays character names above enemy players.\nUseful for recognizing specific players and coordinating with team.\nDisable to reduce screen clutter.");
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawEnemyAllyTab()
    {
        if (ImGui.BeginTabItem("Enemy/Ally"))
        {
            ImGui.TextWrapped("Configure how the plugin distinguishes between enemies and allies.");
            ImGui.Spacing();

            var showEnemies = Configuration.ShowEnemies;
            if (ImGui.Checkbox("Show Lines to Enemies", ref showEnemies))
            {
                Configuration.ShowEnemies = showEnemies;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Draw lines to enemy players.\nThis is the main feature for tracking opponents in PvP.");
            }

            var showAllies = Configuration.ShowAllies;
            if (ImGui.Checkbox("Show Lines to Allies", ref showAllies))
            {
                Configuration.ShowAllies = showAllies;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Draw lines to allied players (party/alliance members).\nUseful for keeping track of your team's position.");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            var showIndicator = Configuration.ShowAllyEnemyIndicator;
            if (ImGui.Checkbox("Show 'ALLY' / 'ENEMY' Labels", ref showIndicator))
            {
                Configuration.ShowAllyEnemyIndicator = showIndicator;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Display text labels above players indicating if they are allies or enemies.\nHelps quickly identify player status in chaotic battles.");
            }

            ImGui.Spacing();
            ImGui.Text("Ally Line Appearance:");

            var allyLineColor = Configuration.AllyLineColor;
            if (ImGui.ColorEdit4("Ally Line Color", ref allyLineColor))
            {
                Configuration.AllyLineColor = allyLineColor;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Color for lines to allied players.\nDefault: Green to indicate friendly status.");
            }

            var allyLineThickness = Configuration.AllyLineThickness;
            if (ImGui.SliderFloat("Ally Line Thickness", ref allyLineThickness, 1.0f, 10.0f))
            {
                Configuration.AllyLineThickness = allyLineThickness;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Thickness of lines to allied players.\nCan be different from enemy line thickness for easy distinction.");
            }

            var differentColors = Configuration.DifferentColorsForAllies;
            if (ImGui.Checkbox("Use Different Colors for Allies", ref differentColors))
            {
                Configuration.DifferentColorsForAllies = differentColors;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("When enabled, ally lines will use a blend of role colors and ally color.\nWhen disabled, all ally lines use the same ally color.");
            }

            ImGui.Spacing();
            ImGui.TextWrapped("Note: Ally detection works for party and alliance members. In some PvP modes, additional allies (like Grand Company members) may not be automatically detected.");

            ImGui.EndTabItem();
        }
    }

    private void DrawJobRoleTab()
    {
        if (ImGui.BeginTabItem("Jobs & Roles"))
        {
            ImGui.TextWrapped("Configure job and role detection for tactical PvP advantage.");
            ImGui.Spacing();

            var showPlayerJobs = Configuration.ShowPlayerJobs;
            if (ImGui.Checkbox("Show Enemy Jobs/Roles", ref showPlayerJobs))
            {
                Configuration.ShowPlayerJobs = showPlayerJobs;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays job or role information above enemy players.\nHelps identify threats: Healers (priority targets), Tanks (high HP), DPS types.\nEssential for tactical PvP play.");
            }

            if (Configuration.ShowPlayerJobs)
            {
                ImGui.Indent();
                var showJobIcons = Configuration.ShowJobIcons;
                if (ImGui.Checkbox("Show Job Abbreviations (vs Role Names)", ref showJobIcons))
                {
                    Configuration.ShowJobIcons = showJobIcons;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Job Abbreviations: WHM, PLD, NIN, etc.\nRole Names: HEAL, TANK, MDPS, etc.\nJob abbreviations are more specific but take more space.");
                }
                ImGui.Unindent();
            }

            var colorCodeByRole = Configuration.ColorCodeByRole;
            if (ImGui.Checkbox("Color Lines by Role", ref colorCodeByRole))
            {
                Configuration.ColorCodeByRole = colorCodeByRole;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Colors lines based on enemy role:\nBlue = Tanks, Green = Healers, Red = Melee DPS\nOrange = Ranged Physical DPS, Purple = Magical DPS\nOverrides the custom line color setting when enabled.");
            }

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            // Role color preview
            ImGui.TextWrapped("Role Color Preview:");
            ImGui.Spacing();

            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.Tank), "■ TANK - Tanks (PLD, WAR, DRK, GNB)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.Healer), "■ HEAL - Healers (WHM, SCH, AST, SGE)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.MeleeDPS), "■ MDPS - Melee DPS (MNK, DRG, NIN, SAM, RPR, VPR)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.PhysicalRangedDPS), "■ PDPS - Physical Ranged (BRD, MCH, DNC)");
            ImGui.TextColored(JobHelper.GetRoleColor(PlayerRole.MagicalRangedDPS), "■ CDPS - Magical Ranged (BLM, SMN, RDM, PCT)");

            ImGui.EndTabItem();
        }
    }

    private void DrawLowHealthTab()
    {
        if (ImGui.BeginTabItem("Low Health"))
        {
            ImGui.TextWrapped("Configure killable target detection and visual indicators.");
            ImGui.Spacing();

            var showLowHealthIndicator = Configuration.ShowLowHealthIndicator;
            if (ImGui.Checkbox("Enable Low Health Indicator", ref showLowHealthIndicator))
            {
                Configuration.ShowLowHealthIndicator = showLowHealthIndicator;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Highlights enemies with low health as potential kill targets.\nChanges line color, thickness, and adds health information.");
            }

            if (Configuration.ShowLowHealthIndicator)
            {
                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                var lowHealthThreshold = Configuration.LowHealthThreshold;
                if (ImGui.SliderFloat("Low Health Threshold (%)", ref lowHealthThreshold, 5.0f, 50.0f))
                {
                    Configuration.LowHealthThreshold = lowHealthThreshold;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Health percentage below which enemies are marked as 'killable'.\n10-15%: Very low, almost dead\n20-25%: Good for burst combos\n30-40%: Early kill attempt threshold");
                }

                var lowHealthLineColor = Configuration.LowHealthLineColor;
                if (ImGui.ColorEdit4("Low Health Line Color", ref lowHealthLineColor))
                {
                    Configuration.LowHealthLineColor = lowHealthLineColor;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Color for lines to low health enemies.\nRecommended: Gold/Yellow for high visibility, Red for urgency.");
                }

                var lowHealthLineThickness = Configuration.LowHealthLineThickness;
                if (ImGui.SliderFloat("Low Health Line Thickness", ref lowHealthLineThickness, 2.0f, 15.0f))
                {
                    Configuration.LowHealthLineThickness = lowHealthLineThickness;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Thickness of lines to low health enemies.\nThicker lines make killable targets more obvious.");
                }

                var showHealthPercentage = Configuration.ShowHealthPercentage;
                if (ImGui.Checkbox("Show Health Percentage", ref showHealthPercentage))
                {
                    Configuration.ShowHealthPercentage = showHealthPercentage;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Shows exact health percentage (e.g., '15% HP') vs just 'KILLABLE'.\nUseful for precise timing of finishing moves.");
                }

                var pulseKillableTargets = Configuration.PulseKillableTargets;
                if (ImGui.Checkbox("Pulse Killable Targets", ref pulseKillableTargets))
                {
                    Configuration.PulseKillableTargets = pulseKillableTargets;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Makes lines and text for killable targets pulse/animate.\nDraws more attention but may be distracting.");
                }

                ImGui.Spacing();
                ImGui.Separator();
                ImGui.Spacing();

                // Usage tips
                ImGui.TextWrapped("Usage Tips:");
                ImGui.BulletText("Low health targets appear with different colored, thicker lines");
                ImGui.BulletText("Health info appears above the enemy player");
                ImGui.BulletText("Use this to prioritize finishing moves and burst combos");
                ImGui.BulletText("Adjust threshold based on your job's burst potential");
                ImGui.BulletText("Healers at low health are highest priority targets");
            }

            ImGui.EndTabItem();
        }
    }

    private void DrawPerformanceTab()
    {
        if (ImGui.BeginTabItem("Performance"))
        {
            ImGui.TextWrapped("Adjust performance vs responsiveness balance.");
            ImGui.Spacing();

            var updateFreq = Configuration.UpdateFrequency;
            if (ImGui.SliderInt("Update Frequency (ms)", ref updateFreq, 16, 200))
            {
                Configuration.UpdateFrequency = updateFreq;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("How often the plugin refreshes line positions.\n16-30ms: Competitive play, smooth tracking\n60ms: Good balance for most users\n100-120ms: Older computers or multitasking\n150-200ms: Minimal performance impact");
            }

            ImGui.Spacing();
            ImGui.TextColored(new Vector4(0.7f, 0.7f, 0.7f, 1.0f), "Lower values = more responsive but higher CPU usage");
            ImGui.TextColored(new Vector4(0.7f, 0.7f, 0.7f, 1.0f), "Higher values = less responsive but better performance");

            ImGui.Spacing();
            ImGui.Separator();
            ImGui.Spacing();

            // Performance tips
            ImGui.TextWrapped("Performance Tips:");
            ImGui.BulletText("Reduce Max Distance to improve performance");
            ImGui.BulletText("Disable Distance Text and Player Names if not needed");
            ImGui.BulletText("Use Role Names instead of Job Abbreviations");
            ImGui.BulletText("Increase Update Frequency for better performance");

            ImGui.EndTabItem();
        }
    }

    private void DrawPresetsTab()
    {
        if (ImGui.BeginTabItem("Quick Presets"))
        {
            ImGui.TextWrapped("Click a preset to quickly configure settings for your playstyle.");
            ImGui.Spacing();

            if (ImGui.Button("Aggressive Melee (NIN, MNK, DRG)", new Vector2(-1, 40)))
            {
                Configuration.LineColor = new Vector4(1.0f, 0.0f, 0.0f, 1.0f); // Bright Red
                Configuration.LineThickness = 4.0f;
                Configuration.MaxDistance = 30.0f;
                Configuration.ShowDistance = true;
                Configuration.OnlyInPvP = true;
                Configuration.ShowInCombatOnly = true;
                Configuration.ShowPlayerNames = false;
                Configuration.ShowPlayerJobs = true;
                Configuration.ShowJobIcons = false; // Show roles for quick identification
                Configuration.ColorCodeByRole = true;
                Configuration.ShowLowHealthIndicator = true;
                Configuration.LowHealthThreshold = 20.0f; // Aggressive threshold for burst combos
                Configuration.LowHealthLineColor = new Vector4(1.0f, 0.8f, 0.0f, 1.0f); // Gold
                Configuration.LowHealthLineThickness = 6.0f;
                Configuration.ShowHealthPercentage = true;
                Configuration.PulseKillableTargets = true;
                Configuration.UpdateFrequency = 30;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Optimized for close-range combat with high visibility lines.\nRole-based colors, combat-only display, fast updates.");
            }

            if (ImGui.Button("Ranged DPS (BLM, SMN, BRD)", new Vector2(-1, 40)))
            {
                Configuration.LineColor = new Vector4(1.0f, 1.0f, 0.0f, 0.8f); // Yellow
                Configuration.LineThickness = 2.5f;
                Configuration.MaxDistance = 60.0f;
                Configuration.ShowDistance = true;
                Configuration.OnlyInPvP = true;
                Configuration.ShowInCombatOnly = false;
                Configuration.ShowPlayerNames = true;
                Configuration.ShowPlayerJobs = true;
                Configuration.ShowJobIcons = true; // Show specific jobs for targeting
                Configuration.ColorCodeByRole = true;
                Configuration.ShowLowHealthIndicator = true;
                Configuration.LowHealthThreshold = 25.0f; // Standard threshold for ranged burst
                Configuration.LowHealthLineColor = new Vector4(1.0f, 0.5f, 0.0f, 1.0f); // Orange
                Configuration.LowHealthLineThickness = 4.0f;
                Configuration.ShowHealthPercentage = true;
                Configuration.PulseKillableTargets = false; // Less distracting for ranged
                Configuration.UpdateFrequency = 60;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Longer range with constant awareness and target identification.\nShows job abbreviations and player names for precise targeting.");
            }

            if (ImGui.Button("Tank/Support", new Vector2(-1, 40)))
            {
                Configuration.LineColor = new Vector4(0.0f, 0.5f, 1.0f, 0.7f); // Blue
                Configuration.LineThickness = 3.0f;
                Configuration.MaxDistance = 45.0f;
                Configuration.ShowDistance = false;
                Configuration.OnlyInPvP = true;
                Configuration.ShowInCombatOnly = false;
                Configuration.ShowPlayerNames = true;
                Configuration.ShowPlayerJobs = true;
                Configuration.ShowJobIcons = false; // Show roles for team awareness
                Configuration.ColorCodeByRole = true;
                Configuration.ShowLowHealthIndicator = true;
                Configuration.LowHealthThreshold = 30.0f; // Higher threshold for support awareness
                Configuration.LowHealthLineColor = new Vector4(0.8f, 0.8f, 0.0f, 1.0f); // Bright Yellow
                Configuration.LowHealthLineThickness = 4.0f;
                Configuration.ShowHealthPercentage = false; // Just show "KILLABLE"
                Configuration.PulseKillableTargets = false; // Clean interface for support
                Configuration.UpdateFrequency = 80;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Balanced settings for positioning and team awareness.\nShows role names and player names for strategic overview.");
            }

            if (ImGui.Button("Performance Focused", new Vector2(-1, 40)))
            {
                Configuration.LineColor = new Vector4(1.0f, 0.0f, 0.0f, 0.6f); // Semi-transparent Red
                Configuration.LineThickness = 2.0f;
                Configuration.MaxDistance = 35.0f;
                Configuration.ShowDistance = false;
                Configuration.OnlyInPvP = true;
                Configuration.ShowInCombatOnly = true;
                Configuration.ShowPlayerNames = false;
                Configuration.ShowPlayerJobs = true;
                Configuration.ShowJobIcons = false; // Roles only for minimal text
                Configuration.ColorCodeByRole = false; // Use single color for performance
                Configuration.ShowLowHealthIndicator = true;
                Configuration.LowHealthThreshold = 25.0f;
                Configuration.LowHealthLineColor = new Vector4(1.0f, 1.0f, 0.0f, 0.8f); // Simple yellow
                Configuration.LowHealthLineThickness = 3.0f;
                Configuration.ShowHealthPercentage = false; // Just "KILLABLE" for performance
                Configuration.PulseKillableTargets = false; // No animation for performance
                Configuration.UpdateFrequency = 120;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Minimal performance impact while maintaining functionality.\nSingle color, combat-only, reduced features for best performance.");
            }

            ImGui.EndTabItem();
        }
    }
}
