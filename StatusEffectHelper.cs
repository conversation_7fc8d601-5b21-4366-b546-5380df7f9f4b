using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;

namespace PvPLinePlugin;

public enum ThreatLevel
{
    Disabled,       // Crowd controlled, can't act
    Vulnerable,     // Vulnerable to damage, easy target
    Normal,         // Standard threat level
    Defensive,      // Has defensive buffs, harder to kill
    ModerateThreat, // Has offensive buffs, moderate danger
    HighThreat      // In burst mode or limit break, very dangerous
}

public static class StatusEffectHelper
{
    // PvP-specific status effects that matter in combat
    private static readonly Dictionary<uint, StatusInfo> PvPStatusEffects = new()
    {
        // === CROWD CONTROL (High Priority) ===
        { 1343, new("Stun", new(1.0f, 0.8f, 0.0f, 1.0f), true) },           // Generic Stun
        { 2, new("Sleep", new(0.6f, 0.0f, 1.0f, 1.0f), true) },              // Sleep
        { 6, new("Silence", new(0.8f, 0.0f, 0.8f, 1.0f), true) },            // Silence
        { 7, new("Bind", new(0.7f, 0.4f, 0.0f, 1.0f), true) },               // Bind/Root
        { 9, new("Heavy", new(0.5f, 0.5f, 0.5f, 1.0f), true) },              // Heavy (slow)
        { 17, new("Paralysis", new(1.0f, 0.7f, 0.0f, 1.0f), true) },         // Paralysis
        { 3219, new("Deep Freeze", new(0.0f, 0.8f, 1.0f, 1.0f), true) },     // BLM Deep Freeze
        { 1345, new("Bind", new(0.7f, 0.4f, 0.0f, 1.0f), true) },            // PvP Bind

        // === VULNERABILITY & DAMAGE MODIFIERS ===
        { 638, new("Vulnerability", new(1.0f, 0.3f, 0.0f, 1.0f), true) },    // Vulnerability Up
        { 3359, new("Damage Up", new(1.0f, 0.0f, 0.0f, 1.0f), false) },      // PvP Damage Up
        { 3360, new("Damage Down", new(0.0f, 0.8f, 0.0f, 1.0f), true) },     // PvP Damage Down
        { 1347, new("Physical Vulnerability", new(1.0f, 0.4f, 0.0f, 1.0f), true) }, // Physical Vuln
        { 1348, new("Magic Vulnerability", new(1.0f, 0.2f, 0.8f, 1.0f), true) },    // Magic Vuln

        // === DEFENSIVE BUFFS ===
        { 1362, new("Guard", new(0.0f, 0.0f, 1.0f, 1.0f), false) },          // Guard (90% damage reduction)
        { 3054, new("Recuperate", new(0.0f, 1.0f, 0.5f, 1.0f), false) },     // Recuperate (healing)
        { 1191, new("Stoneskin", new(0.6f, 0.6f, 0.6f, 1.0f), false) },      // Stoneskin
        { 3358, new("Resilience", new(0.0f, 0.7f, 1.0f, 1.0f), false) },     // PvP Resilience

        // === MOVEMENT & POSITIONING ===
        { 1418, new("Sprint", new(1.0f, 1.0f, 1.0f, 1.0f), false) },         // Sprint
        { 3357, new("Elusive Jump", new(0.8f, 0.8f, 1.0f, 1.0f), false) },   // DRG Elusive Jump
        { 1368, new("Stealth", new(0.3f, 0.3f, 0.3f, 1.0f), true) },         // Stealth/Hide

        // === HEALING OVER TIME ===
        { 158, new("Regen", new(0.0f, 1.0f, 0.0f, 1.0f), false) },           // Regen
        { 1330, new("Medica II", new(0.0f, 0.9f, 0.0f, 1.0f), false) },      // Medica II
        { 3361, new("Healing Over Time", new(0.0f, 0.8f, 0.2f, 1.0f), false) }, // Generic HoT

        // === JOB-SPECIFIC IMPORTANT BUFFS ===
        // Melee DPS
        { 3220, new("Melee Combo", new(1.0f, 0.5f, 0.0f, 1.0f), false) },    // Combo state
        { 3221, new("Burst Mode", new(1.0f, 0.0f, 0.0f, 1.0f), false) },     // Burst window

        // Ranged Physical
        { 3222, new("Ranged Combo", new(0.8f, 0.8f, 0.0f, 1.0f), false) },   // Ranged combo

        // Magical DPS
        { 3223, new("Polyglot", new(0.8f, 0.0f, 1.0f, 1.0f), false) },       // BLM Polyglot
        { 3224, new("Dualcast", new(0.6f, 0.0f, 1.0f, 1.0f), false) },       // RDM Dualcast

        // Tank
        { 3225, new("Tank Stance", new(0.0f, 0.0f, 0.8f, 1.0f), false) },    // Tank stance

        // Healer
        { 3226, new("Healing Boost", new(0.0f, 1.0f, 0.8f, 1.0f), false) },  // Healing potency up

        // === LIMIT BREAK ===
        { 3227, new("Limit Break", new(1.0f, 0.8f, 0.0f, 1.0f), false) },    // LB active

        // === DEBUFFS FROM SPECIFIC ABILITIES ===
        { 3228, new("Poison", new(0.5f, 1.0f, 0.0f, 1.0f), true) },          // DoT effects
        { 3229, new("Burn", new(1.0f, 0.3f, 0.0f, 1.0f), true) },            // Fire DoT
        { 3230, new("Frostbite", new(0.0f, 0.5f, 1.0f, 1.0f), true) },       // Ice DoT
    };

    // Pre-computed sets for faster lookups
    private static readonly HashSet<uint> VulnerabilityStatusIds = [638, 1347, 1348];
    private static readonly HashSet<uint> CrowdControlStatusIds = [1343, 2, 6, 7, 9, 17, 3219, 1345];
    private static readonly HashSet<uint> DefensiveBuffIds = [1362, 3054, 1191, 3358];
    private static readonly HashSet<uint> OffensiveBuffIds = [3359, 3220, 3221, 3222, 3227];
    private static readonly HashSet<uint> HealingEffectIds = [158, 1330, 3361, 3226];

    public static List<StatusInfo> GetImportantStatusEffects(IPlayerCharacter player, bool onlyNegative = false)
    {
        if (player.StatusList == null) return [];

        return [.. player.StatusList
            .Where(status => PvPStatusEffects.TryGetValue(status.StatusId, out var statusInfo) &&
                           (!onlyNegative || statusInfo.IsNegative))
            .Select(status => new StatusInfo(
                PvPStatusEffects[status.StatusId].Name,
                PvPStatusEffects[status.StatusId].Color,
                PvPStatusEffects[status.StatusId].IsNegative,
                status.RemainingTime))];
    }

    public static bool HasImportantStatusEffects(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => PvPStatusEffects.ContainsKey(status.StatusId)) ?? false;
    }

    // === VULNERABILITY DETECTION ===
    public static bool IsVulnerable(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => VulnerabilityStatusIds.Contains(status.StatusId)) ?? false;
    }

    // === CROWD CONTROL DETECTION ===
    public static bool IsCrowdControlled(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => CrowdControlStatusIds.Contains(status.StatusId)) ?? false;
    }

    public static bool IsStunned(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 1343) ?? false;
    }

    public static bool IsSilenced(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 6) ?? false;
    }

    public static bool IsBound(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId is 7 or 1345) ?? false;
    }

    // === DEFENSIVE STATE DETECTION ===
    public static bool HasDefensiveBuff(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => DefensiveBuffIds.Contains(status.StatusId)) ?? false;
    }

    public static bool IsGuarding(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 1362) ?? false;
    }

    public static bool IsRecuperating(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 3054) ?? false;
    }

    // === OFFENSIVE STATE DETECTION ===
    public static bool HasOffensiveBuff(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => OffensiveBuffIds.Contains(status.StatusId)) ?? false;
    }

    public static bool IsInBurstMode(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId is 3221 or 3227) ?? false; // Burst or LB
    }

    // === HEALING STATE DETECTION ===
    public static bool HasHealingEffect(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => HealingEffectIds.Contains(status.StatusId)) ?? false;
    }

    // === MOVEMENT STATE DETECTION ===
    public static bool IsSprinting(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 1418) ?? false;
    }

    public static bool IsStealthed(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 1368) ?? false;
    }

    // === THREAT LEVEL ASSESSMENT ===
    public static ThreatLevel GetThreatLevel(IPlayerCharacter player)
    {
        if (IsCrowdControlled(player)) return ThreatLevel.Disabled;
        if (IsGuarding(player)) return ThreatLevel.Defensive;
        if (IsInBurstMode(player)) return ThreatLevel.HighThreat;
        if (HasOffensiveBuff(player)) return ThreatLevel.ModerateThreat;
        if (IsVulnerable(player)) return ThreatLevel.Vulnerable;
        return ThreatLevel.Normal;
    }

    // === PRIORITY TARGET ASSESSMENT ===
    public static bool IsHighPriorityTarget(IPlayerCharacter player)
    {
        return IsVulnerable(player) || IsCrowdControlled(player) || !HasDefensiveBuff(player);
    }

    public static bool IsDangerousEnemy(IPlayerCharacter player)
    {
        return IsInBurstMode(player) || HasOffensiveBuff(player) || IsSprinting(player);
    }
}

public class StatusInfo(string name, Vector4 color, bool isNegative, float? remainingTime = null)
{
    public string Name { get; } = name;
    public Vector4 Color { get; } = color;
    public bool IsNegative { get; } = isNegative;
    public float? RemainingTime { get; } = remainingTime;
}
