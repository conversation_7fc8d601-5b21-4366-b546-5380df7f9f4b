using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;

namespace PvPLinePlugin;

public static class StatusEffectHelper
{
    // Important status effects to track in PvP
    private static readonly Dictionary<uint, StatusInfo> ImportantStatusEffects = new()
    {
        // Vulnerability and Damage
        { 638, new("Vulnerability Up", new(1.0f, 0.5f, 0.0f, 1.0f), true) },
        { 714, new("Damage Up", new(1.0f, 0.0f, 0.0f, 1.0f), false) },
        { 715, new("Damage Down", new(0.0f, 1.0f, 0.0f, 1.0f), true) },

        // Crowd Control
        { 2, new("Sleep", new(0.5f, 0.0f, 1.0f, 1.0f), true) },
        { 3, new("Stun", new(1.0f, 1.0f, 0.0f, 1.0f), true) },
        { 6, new("Silence", new(0.8f, 0.0f, 0.8f, 1.0f), true) },
        { 7, new("Bind", new(0.6f, 0.3f, 0.0f, 1.0f), true) },
        { 9, new("Heavy", new(0.4f, 0.4f, 0.4f, 1.0f), true) },
        { 17, new("Paralysis", new(1.0f, 0.8f, 0.0f, 1.0f), true) },

        // Defensive Buffs
        { 1191, new("Stoneskin", new(0.5f, 0.5f, 0.5f, 1.0f), false) },
        { 1362, new("Protect", new(0.0f, 0.8f, 1.0f, 1.0f), false) },
        { 1363, new("Shell", new(0.8f, 0.0f, 1.0f, 1.0f), false) },

        // Healing Over Time
        { 158, new("Regen", new(0.0f, 1.0f, 0.0f, 1.0f), false) },
        { 1330, new("Medica II", new(0.0f, 0.8f, 0.0f, 1.0f), false) },

        // PvP Specific
        { 1343, new("Guard", new(0.0f, 0.0f, 1.0f, 1.0f), false) },
        { 3054, new("Recuperate", new(0.0f, 1.0f, 0.5f, 1.0f), false) },
        { 1418, new("Sprint", new(1.0f, 1.0f, 1.0f, 1.0f), false) },

        // Stealth and Invisibility
        { 1368, new("Stealth", new(0.3f, 0.3f, 0.3f, 1.0f), true) },
        { 1369, new("Invisible", new(0.2f, 0.2f, 0.2f, 1.0f), true) },
    };

    // Pre-computed sets for faster lookups
    private static readonly HashSet<uint> VulnerabilityStatusIds = [638];
    private static readonly HashSet<uint> CrowdControlStatusIds = [2, 3, 6, 7, 17];

    public static List<StatusInfo> GetImportantStatusEffects(IPlayerCharacter player, bool onlyNegative = false)
    {
        if (player.StatusList == null) return [];

        return player.StatusList
            .Where(status => ImportantStatusEffects.TryGetValue(status.StatusId, out var statusInfo) &&
                           (!onlyNegative || statusInfo.IsNegative))
            .Select(status => new StatusInfo(
                ImportantStatusEffects[status.StatusId].Name,
                ImportantStatusEffects[status.StatusId].Color,
                ImportantStatusEffects[status.StatusId].IsNegative,
                status.RemainingTime))
            .ToList();
    }

    public static bool HasImportantStatusEffects(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => ImportantStatusEffects.ContainsKey(status.StatusId)) ?? false;
    }

    public static bool IsVulnerable(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => VulnerabilityStatusIds.Contains(status.StatusId)) ?? false;
    }

    public static bool IsCrowdControlled(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => CrowdControlStatusIds.Contains(status.StatusId)) ?? false;
    }
}

public class StatusInfo(string name, Vector4 color, bool isNegative, float? remainingTime = null)
{
    public string Name { get; } = name;
    public Vector4 Color { get; } = color;
    public bool IsNegative { get; } = isNegative;
    public float? RemainingTime { get; } = remainingTime;
}
