using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;

namespace PvPLinePlugin;

public enum ThreatLevel
{
    Disabled,       // Crowd controlled, can't act
    Vulnerable,     // Vulnerable to damage, easy target
    Normal,         // Standard threat level
    Defensive,      // Has defensive buffs, harder to kill
    ModerateThreat, // Has offensive buffs, moderate danger
    HighThreat      // In burst mode or limit break, very dangerous
}

public static class StatusEffectHelper
{
    // Actual FFXIV PvP status effects with correct IDs
    private static readonly Dictionary<uint, StatusInfo> PvPStatusEffects = new()
    {
        // === CROWD CONTROL (High Priority) ===
        { 1343, new("Stun", new(1.0f, 0.8f, 0.0f, 1.0f), true) },           // Stun
        { 2, new("Sleep", new(0.6f, 0.0f, 1.0f, 1.0f), true) },              // Sleep
        { 6, new("Silence", new(0.8f, 0.0f, 0.8f, 1.0f), true) },            // Silence
        { 7, new("Bind", new(0.7f, 0.4f, 0.0f, 1.0f), true) },               // Bind
        { 9, new("Heavy", new(0.5f, 0.5f, 0.5f, 1.0f), true) },              // Heavy
        { 17, new("Paralysis", new(1.0f, 0.7f, 0.0f, 1.0f), true) },         // Paralysis
        { 13, new("Slow", new(0.6f, 0.6f, 0.6f, 1.0f), true) },              // Slow
        { 3219, new("Deep Freeze", new(0.0f, 0.8f, 1.0f, 1.0f), true) },     // BLM Deep Freeze

        // === VULNERABILITY & DAMAGE MODIFIERS ===
        { 638, new("Vulnerability Up", new(1.0f, 0.3f, 0.0f, 1.0f), true) }, // Vulnerability Up
        { 714, new("Damage Up", new(1.0f, 0.0f, 0.0f, 1.0f), false) },       // Damage Up
        { 715, new("Damage Down", new(0.0f, 0.8f, 0.0f, 1.0f), true) },      // Damage Down

        // === DEFENSIVE BUFFS ===
        { 1362, new("Guard", new(0.0f, 0.0f, 1.0f, 1.0f), false) },          // Guard (90% damage reduction)
        { 3054, new("Recuperate", new(0.0f, 1.0f, 0.5f, 1.0f), false) },     // Recuperate
        { 1191, new("Stoneskin", new(0.6f, 0.6f, 0.6f, 1.0f), false) },      // Stoneskin

        // === MOVEMENT ===
        { 1418, new("Sprint", new(1.0f, 1.0f, 1.0f, 1.0f), false) },         // Sprint

        // === HEALING OVER TIME ===
        { 158, new("Regen", new(0.0f, 1.0f, 0.0f, 1.0f), false) },           // Regen
        { 1330, new("Medica II", new(0.0f, 0.9f, 0.0f, 1.0f), false) },      // Medica II

        // === COMMON DEBUFFS ===
        { 18, new("Poison", new(0.5f, 1.0f, 0.0f, 1.0f), true) },            // Poison
        { 269, new("Bio", new(0.3f, 0.8f, 0.0f, 1.0f), true) },              // Bio
        { 143, new("Miasma", new(0.4f, 0.6f, 0.8f, 1.0f), true) },           // Miasma

        // === JOB-SPECIFIC IMPORTANT EFFECTS ===
        // Black Mage
        { 1211, new("Polyglot", new(0.8f, 0.0f, 1.0f, 1.0f), false) },       // Polyglot
        { 1249, new("Firestarter", new(1.0f, 0.5f, 0.0f, 1.0f), false) },    // Firestarter
        { 164, new("Thundercloud", new(0.8f, 0.8f, 0.0f, 1.0f), false) },    // Thundercloud

        // Red Mage
        { 1249, new("Dualcast", new(0.6f, 0.0f, 1.0f, 1.0f), false) },       // Dualcast
        { 1234, new("Verfire Ready", new(1.0f, 0.3f, 0.0f, 1.0f), false) },  // Verfire Ready
        { 1235, new("Verstone Ready", new(0.0f, 0.8f, 0.5f, 1.0f), false) }, // Verstone Ready

        // Summoner
        { 1803, new("Further Ruin", new(0.8f, 0.0f, 0.8f, 1.0f), false) },   // Further Ruin

        // Melee DPS
        { 1833, new("True North", new(0.0f, 0.8f, 1.0f, 1.0f), false) },     // True North

        // Tanks
        { 91, new("Defiance", new(0.8f, 0.0f, 0.0f, 1.0f), false) },         // Defiance (WAR)
        { 79, new("Iron Will", new(0.0f, 0.0f, 0.8f, 1.0f), false) },        // Iron Will (PLD)
        { 743, new("Grit", new(0.3f, 0.3f, 0.3f, 1.0f), false) },            // Grit (DRK)
        { 1833, new("Royal Guard", new(0.5f, 0.0f, 0.5f, 1.0f), false) },    // Royal Guard (GNB)

        // Healers
        { 1871, new("Presence of Mind", new(0.0f, 1.0f, 1.0f, 1.0f), false) }, // Presence of Mind (WHM)
        { 1213, new("Lightspeed", new(1.0f, 1.0f, 0.0f, 1.0f), false) },     // Lightspeed (AST)
    };

    // Pre-computed sets for faster lookups
    private static readonly HashSet<uint> VulnerabilityStatusIds = [638];
    private static readonly HashSet<uint> CrowdControlStatusIds = [1343, 2, 6, 7, 9, 17, 13, 3219];
    private static readonly HashSet<uint> DefensiveBuffIds = [1362, 3054, 1191];
    private static readonly HashSet<uint> OffensiveBuffIds = [714, 1249, 164, 1234, 1235, 1803];
    private static readonly HashSet<uint> HealingEffectIds = [158, 1330];
    private static readonly HashSet<uint> TankStanceIds = [91, 79, 743, 1833];
    private static readonly HashSet<uint> DebuffIds = [18, 269, 143];

    public static List<StatusInfo> GetImportantStatusEffects(IPlayerCharacter player, bool onlyNegative = false)
    {
        if (player.StatusList == null) return [];

        return [.. player.StatusList
            .Where(status => PvPStatusEffects.TryGetValue(status.StatusId, out var statusInfo) &&
                           (!onlyNegative || statusInfo.IsNegative))
            .Select(status => new StatusInfo(
                PvPStatusEffects[status.StatusId].Name,
                PvPStatusEffects[status.StatusId].Color,
                PvPStatusEffects[status.StatusId].IsNegative,
                status.RemainingTime))];
    }

    public static bool HasImportantStatusEffects(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => PvPStatusEffects.ContainsKey(status.StatusId)) ?? false;
    }

    // === VULNERABILITY DETECTION ===
    public static bool IsVulnerable(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => VulnerabilityStatusIds.Contains(status.StatusId)) ?? false;
    }

    // === CROWD CONTROL DETECTION ===
    public static bool IsCrowdControlled(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => CrowdControlStatusIds.Contains(status.StatusId)) ?? false;
    }

    public static bool IsStunned(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 1343) ?? false;
    }

    public static bool IsSilenced(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 6) ?? false;
    }

    public static bool IsBound(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId is 7 or 1345) ?? false;
    }

    // === DEFENSIVE STATE DETECTION ===
    public static bool HasDefensiveBuff(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => DefensiveBuffIds.Contains(status.StatusId)) ?? false;
    }

    public static bool IsGuarding(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 1362) ?? false;
    }

    public static bool IsRecuperating(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 3054) ?? false;
    }

    // === OFFENSIVE STATE DETECTION ===
    public static bool HasOffensiveBuff(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => OffensiveBuffIds.Contains(status.StatusId)) ?? false;
    }

    public static bool HasDamageUp(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 714) ?? false;
    }

    // === TANK DETECTION ===
    public static bool HasTankStance(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => TankStanceIds.Contains(status.StatusId)) ?? false;
    }

    // === DEBUFF DETECTION ===
    public static bool HasDoTEffect(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => DebuffIds.Contains(status.StatusId)) ?? false;
    }

    // === HEALING STATE DETECTION ===
    public static bool HasHealingEffect(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => HealingEffectIds.Contains(status.StatusId)) ?? false;
    }

    // === MOVEMENT STATE DETECTION ===
    public static bool IsSprinting(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 1418) ?? false;
    }

    public static bool IsStealthed(IPlayerCharacter player)
    {
        return player.StatusList?.Any(status => status.StatusId == 1368) ?? false;
    }

    // === THREAT LEVEL ASSESSMENT ===
    public static ThreatLevel GetThreatLevel(IPlayerCharacter player)
    {
        if (IsCrowdControlled(player)) return ThreatLevel.Disabled;
        if (IsGuarding(player)) return ThreatLevel.Defensive;
        if (IsInBurstMode(player)) return ThreatLevel.HighThreat;
        if (HasOffensiveBuff(player)) return ThreatLevel.ModerateThreat;
        if (IsVulnerable(player)) return ThreatLevel.Vulnerable;
        return ThreatLevel.Normal;
    }

    // === PRIORITY TARGET ASSESSMENT ===
    public static bool IsHighPriorityTarget(IPlayerCharacter player)
    {
        return IsVulnerable(player) || IsCrowdControlled(player) || !HasDefensiveBuff(player);
    }

    public static bool IsDangerousEnemy(IPlayerCharacter player)
    {
        return IsInBurstMode(player) || HasOffensiveBuff(player) || IsSprinting(player);
    }
}

public class StatusInfo(string name, Vector4 color, bool isNegative, float? remainingTime = null)
{
    public string Name { get; } = name;
    public Vector4 Color { get; } = color;
    public bool IsNegative { get; } = isNegative;
    public float? RemainingTime { get; } = remainingTime;
}
