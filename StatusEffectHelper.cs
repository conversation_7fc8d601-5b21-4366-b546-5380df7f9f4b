using System.Collections.Generic;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;
using Dalamud.Game.ClientState.Statuses;

namespace PvPLinePlugin;

public static class StatusEffectHelper
{
    // Important status effects to track in PvP
    private static readonly Dictionary<uint, StatusInfo> ImportantStatusEffects = new()
    {
        // Vulnerability and Damage
        { 638, new StatusInfo("Vulnerability Up", new Vector4(1.0f, 0.5f, 0.0f, 1.0f), true) },
        { 714, new StatusInfo("Damage Up", new Vector4(1.0f, 0.0f, 0.0f, 1.0f), false) },
        { 715, new StatusInfo("Damage Down", new Vector4(0.0f, 1.0f, 0.0f, 1.0f), true) },
        
        // Crowd Control
        { 2, new StatusInfo("Sleep", new Vector4(0.5f, 0.0f, 1.0f, 1.0f), true) },
        { 3, new StatusInfo("Stun", new Vector4(1.0f, 1.0f, 0.0f, 1.0f), true) },
        { 6, new StatusInfo("Silence", new Vector4(0.8f, 0.0f, 0.8f, 1.0f), true) },
        { 7, new StatusInfo("Bind", new Vector4(0.6f, 0.3f, 0.0f, 1.0f), true) },
        { 9, new StatusInfo("Heavy", new Vector4(0.4f, 0.4f, 0.4f, 1.0f), true) },
        { 17, new StatusInfo("Paralysis", new Vector4(1.0f, 0.8f, 0.0f, 1.0f), true) },
        
        // Defensive Buffs
        { 1191, new StatusInfo("Stoneskin", new Vector4(0.5f, 0.5f, 0.5f, 1.0f), false) },
        { 1362, new StatusInfo("Protect", new Vector4(0.0f, 0.8f, 1.0f, 1.0f), false) },
        { 1363, new StatusInfo("Shell", new Vector4(0.8f, 0.0f, 1.0f, 1.0f), false) },
        
        // Healing Over Time
        { 158, new StatusInfo("Regen", new Vector4(0.0f, 1.0f, 0.0f, 1.0f), false) },
        { 1330, new StatusInfo("Medica II", new Vector4(0.0f, 0.8f, 0.0f, 1.0f), false) },
        
        // PvP Specific
        { 1343, new StatusInfo("Guard", new Vector4(0.0f, 0.0f, 1.0f, 1.0f), false) },
        { 3054, new StatusInfo("Recuperate", new Vector4(0.0f, 1.0f, 0.5f, 1.0f), false) },
        { 1418, new StatusInfo("Sprint", new Vector4(1.0f, 1.0f, 1.0f, 1.0f), false) },
        
        // Stealth and Invisibility
        { 1368, new StatusInfo("Stealth", new Vector4(0.3f, 0.3f, 0.3f, 1.0f), true) },
        { 1369, new StatusInfo("Invisible", new Vector4(0.2f, 0.2f, 0.2f, 1.0f), true) },
    };

    public static List<StatusInfo> GetImportantStatusEffects(IPlayerCharacter player, bool onlyNegative = false)
    {
        var importantEffects = new List<StatusInfo>();
        
        if (player.StatusList == null) return importantEffects;

        foreach (var status in player.StatusList)
        {
            if (ImportantStatusEffects.TryGetValue(status.StatusId, out var statusInfo))
            {
                if (!onlyNegative || statusInfo.IsNegative)
                {
                    var effectInfo = new StatusInfo(
                        statusInfo.Name,
                        statusInfo.Color,
                        statusInfo.IsNegative,
                        status.RemainingTime
                    );
                    importantEffects.Add(effectInfo);
                }
            }
        }

        return importantEffects;
    }

    public static bool HasImportantStatusEffects(IPlayerCharacter player)
    {
        if (player.StatusList == null) return false;

        foreach (var status in player.StatusList)
        {
            if (ImportantStatusEffects.ContainsKey(status.StatusId))
                return true;
        }

        return false;
    }

    public static bool IsVulnerable(IPlayerCharacter player)
    {
        if (player.StatusList == null) return false;

        foreach (var status in player.StatusList)
        {
            if (status.StatusId == 638) // Vulnerability Up
                return true;
        }

        return false;
    }

    public static bool IsCrowdControlled(IPlayerCharacter player)
    {
        if (player.StatusList == null) return false;

        var ccStatusIds = new uint[] { 2, 3, 6, 7, 17 }; // Sleep, Stun, Silence, Bind, Paralysis

        foreach (var status in player.StatusList)
        {
            if (System.Array.Exists(ccStatusIds, id => id == status.StatusId))
                return true;
        }

        return false;
    }
}

public class StatusInfo
{
    public string Name { get; }
    public Vector4 Color { get; }
    public bool IsNegative { get; }
    public float? RemainingTime { get; }

    public StatusInfo(string name, Vector4 color, bool isNegative, float? remainingTime = null)
    {
        Name = name;
        Color = color;
        IsNegative = isNegative;
        RemainingTime = remainingTime;
    }
}
